import { ref, nextTick, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { deleteUserApp, addUserApp } from '@/api/navbar'
import { deleteFolder } from '@/api/folder'
import { saveFolder } from '@/api/folder'
import { useNavigationStore } from '@/stores/navigation'
import autoBackupManager from '@/utils/autoBackupManager'

/**
 * 应用管理功能
 * @returns {Object} - 返回应用管理相关的状态和方法
 */
export function useAppManager() {
  // 默认文件夹图标
  const defaultFolderIcon = ref('📁')

  // 确认删除应用
  function confirmDeleteApp(app, navigationStore, categoryApps, saveAppOrder) {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除 ${app.name} 吗？`,
      okText: '确认',
      cancelText: '取消',
      centered: true, // 让对话框垂直居中显示
      onOk() {
        const currentApps = app
        const token = localStorage.getItem('token')
        if (token && currentApps.type == 'app') {
          deleteUserApp(currentApps.id).then((res) => {
            if (res.status == 200) {
              console.log('删除成功')
            }
          })
        } else if (token && currentApps.type == 'folder') {
          deleteFolder(currentApps.id).then((res) => {
            if (res.status == 200) {
              console.log('删除成功')
            }
          })
        }
        // 从navigationStore中删除
        const appIndex = navigationStore.allApps.findIndex(a => a.id === app.id);
        if (appIndex !== -1) {
          navigationStore.allApps.splice(appIndex, 1);
        }

        // 从categoryApps中删除
        const category = app.category;
        const categoryAppIndex = navigationStore.categoryApps[category].findIndex(a => a.id === app.id);
        if (categoryAppIndex !== -1) {
          navigationStore.categoryApps[category].splice(categoryAppIndex, 1);
        }

        // 保存更新
        saveAppOrder();

        // 触发自动备份
        autoBackupManager.triggerAutoBackup();
      }
    });
  }

  // 创建新文件夹的函数
  async function createFolder(activeCategory, saveAppOrder) {
    const navigationStore = useNavigationStore();
    
    // 创建一个响应式变量来存储输入值
    let folderNameValue = '新建文件夹';

    // 使用Modal弹出一个对话框，让用户输入文件夹名称
    Modal.confirm({
      title: '创建文件夹',
      centered: true, // 让对话框垂直居中显示
      content: h('div', {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
          alignItems: 'flex-start'
        }
      }, [
        h('p', {
          style: {
            margin: '5px'
          }
        }, '请输入文件夹名称：'),
        h('input', {
          style: {
            width: '100%',
            marginTop: '8px',
            padding: '8px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            margin: '0px'
          },
          value: folderNameValue,
          onInput: (e) => {
            folderNameValue = e.target.value;
          },
          onMounted: (el) => {
            // 自动选中输入框内容
            setTimeout(() => {
              el.select();
            }, 100);
          }
        })
      ]),
      onOk: async () => {
        // 使用存储的值作为文件夹名称
        const folderName = folderNameValue || '新建文件夹';
        const obj = navigationStore.categories.find((item) => item.type == activeCategory.value)
        
        // 生成唯一ID
        let uniqueId = `folder-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
        const token = localStorage.getItem('token')
        if (token && obj) {
          const data = {
            name: folderName,
            id: obj.id,
          }
          try {
            const res = await saveFolder(data);
            if (res.status == 200) {
              uniqueId = res.idTage
            }
          } catch (error) {
            console.error('保存文件夹失败:', error);
          }
        }

        // 创建一个新的文件夹
        const newFolder = {
          id: uniqueId,
          name: folderName,
          type: 'folder',
          category: activeCategory.value,
          icon: defaultFolderIcon.value,
          size: { w: 2, h: 2 },
          color: '#f5ba42',  // 恢复文件夹颜色
          children: [],
          x: 0,
          y: 0,
        }

        // 检查是否可以添加文件夹
        const checkResult = canAddAppToCategory(newFolder, activeCategory.value);

        if (!checkResult.canAdd) {
          message.error(checkResult.reason);
          return;
        }

        // 如果有警告信息，显示警告
        if (checkResult.warning) {
          message.warning(checkResult.warning);
        }

        // 使用 navigation store 的方法添加文件夹
        const addResult = navigationStore.addApp(newFolder, activeCategory.value)

        if (!addResult) {
          message.error('创建文件夹失败：存在ID冲突')
          return
        }

        // 保存更新
        saveAppOrder()

        // 显示成功提示
        message.success(`${folderName} 创建成功！`)

        // 触发自动备份
        autoBackupManager.triggerAutoBackup()
      },
      onCancel: () => {
        // 用户取消创建文件夹，不执行任何操作
      },
      okText: '创建',
      cancelText: '取消',
      width: 360
    });
  }

  // ID重复检查工具函数
  function checkDuplicateId(id, categoryType) {
    const navigationStore = useNavigationStore()
    const apps = navigationStore.categoryApps[categoryType] || []
    return apps.find(app => app.id === id)
  }

  // 检查是否可以添加应用到分类（考虑文件夹和用户应用共存规则）
  function canAddAppToCategory(newApp, categoryType) {
    const existingApp = checkDuplicateId(newApp.id, categoryType)

    if (!existingApp) {
      return { canAdd: true, reason: '' }
    }

    // 如果是相同类型的应用，不允许重复
    if (existingApp.type === newApp.type) {
      return {
        canAdd: false,
        reason: `应用重复添加`
      }
    }

    // 文件夹和普通应用可以共存，但不允许拖动
    if ((existingApp.type === 'folder' && newApp.type !== 'folder') ||
      (existingApp.type !== 'folder' && newApp.type === 'folder')) {
      return {
        canAdd: true,
        reason: '',
        warning: `注意：该ID已被${existingApp.type === 'folder' ? '文件夹' : '应用'}使用，两者可以共存但不能相互拖拽`
      }
    }

    return { canAdd: false, reason: '存在ID冲突' }
  }

  // 处理添加自定义图标
  async function handleAddIcon(iconData, activeCategory, saveAppOrder) {
    const navigationStore = useNavigationStore();
    const obj = navigationStore.categories.find((item) => item.type == activeCategory.value)
    
    // 生成唯一ID
    const token = localStorage.getItem('token')
    if (token && obj) {
      if (iconData.custom == true) {
        const data = {
          name: iconData.name,
          id: obj.id,
          type: 2,
          url: iconData.url,
          logo: iconData.icon
        }
        try {
          const res = await addUserApp(data);
          if (res.status == 200) {
            console.log('新增成功')
          }
        } catch (error) {
          console.error('新增应用失败:', error);
        }
      } else {
        const data = {
          appId: iconData.id || `app-${Date.now()}-${Math.floor(Math.random() * 10000)}`,
          id: obj.id,
          type: 1
        }
        try {
          const res = await addUserApp(data);
          if (res.status == 200) {
            console.log('新增成功')
          }
        } catch (error) {
          console.error('新增应用失败:', error);
        }
      }
    }

    // 生成更可靠的唯一ID - 使用字符串ID确保不会有数值比较问题
    const uniqueId = iconData.id || `app-${Date.now()}-${Math.floor(Math.random() * 10000)}`;

    // 确定应用的目标分类
    const targetCategory = iconData.category || activeCategory.value;

    // 创建临时应用对象用于检查
    const tempApp = {
      id: uniqueId,
      type: iconData.type,
      name: iconData.name
    };

    // 检查是否可以添加到目标分类
    const checkResult = canAddAppToCategory(tempApp, targetCategory);

    if (!checkResult.canAdd) {
      message.error(checkResult.reason);
      return;
    }

    // 如果有警告信息，显示警告
    if (checkResult.warning) {
      message.warning(checkResult.warning);
    }

    // 处理尺寸
    const sizeMap = {
      '1x1': { w: 1, h: 1 },
      '1x2': { w: 1, h: 2 },
      '1x3': { w: 1, h: 3 },
      '1x4': { w: 1, h: 4 },
      '1x5': { w: 1, h: 5 },
      '2x1': { w: 2, h: 1 },
      '2x2': { w: 2, h: 2 },
      '2x3': { w: 2, h: 3 },
      '2x4': { w: 2, h: 4 },
      '2x5': { w: 2, h: 5 },
      '3x1': { w: 3, h: 1 },
      '3x2': { w: 3, h: 2 },
      '3x3': { w: 3, h: 3 },
      '3x4': { w: 3, h: 4 },
      '3x5': { w: 3, h: 5 },
      '4x1': { w: 4, h: 1 },
      '4x2': { w: 4, h: 2 },
      '4x3': { w: 4, h: 3 },
      '4x4': { w: 4, h: 4 },
      '4x5': { w: 4, h: 5 },
      '4x6': { w: 4, h: 6 },
      '6x4': { w: 6, h: 4 }
    }

    // 创建新的应用对象
    const newApp = {
      id: uniqueId,
      name: iconData.name,
      color: iconData.color,
      category: targetCategory, // 使用确定的目标分类
      size: sizeMap[iconData.size] || { w: 1, h: 1 },
      x: 100,
      y: 100,
      type: iconData.type,
      url: iconData.url,
      iscanopen: iconData.iscanopen
    }

    // 根据类型设置不同属性
    if (iconData.type === 'app') {
      newApp.icon = iconData.icon || iconData.iconUrl // 支持icon或iconUrl
      newApp.description = iconData.description
    } else if (iconData.type === 'card') {
      // 卡片类型特殊处理
      newApp.websiteAddress = iconData.websiteAddress;
      newApp.headerColor = iconData.headerColor || '#4285F4';
    } else if (iconData.type === 'folder') {
      // 文件夹类型特殊处理
      newApp.icon = iconData.iconUrl || defaultFolderIcon.value
      newApp.size = { w: 3, h: 3 } // 文件夹固定为3x3大小
      newApp.children = [] // 添加children数组存放文件夹内应用
    }

    // 直接使用 navigation store 的方法添加应用
    const addResult = navigationStore.addApp(newApp, targetCategory)

    if (!addResult) {
      message.error('添加失败：存在ID冲突或其他错误')
      return
    }

    // 如果不在同一分类，提示用户
    if (activeCategory.value !== targetCategory && activeCategory.value !== 'all') {
      message.success(`应用已添加到"${targetCategory}"分类，切换到该分类可以查看`);
    }
    
    // 保存更新
    saveAppOrder();

    // 提示成功
    message.success({
      content: '图标/组件添加成功！'
    })

    // 触发自动备份
    autoBackupManager.triggerAutoBackup()
  }

  return {
    defaultFolderIcon,
    confirmDeleteApp,
    createFolder,
    checkDuplicateId,
    canAddAppToCategory,
    handleAddIcon
  }
}