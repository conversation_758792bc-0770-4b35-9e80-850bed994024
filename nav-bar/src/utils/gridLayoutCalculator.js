/**
 * 智能网格布局计算器
 * 根据桌面分辨率、窗口占比等参数智能计算最佳网格布局
 */

import { getGridLayoutConfig } from '@/api/gridConfig.js'

// 接口配置缓存
let API_CONFIG_CACHE = null
let API_CONFIG_LOADING = false

// 计算结果缓存
let CALCULATION_CACHE = new Map()
const CALCULATION_CACHE_MAX_SIZE = 50 // 最大缓存条目数
const CALCULATION_CACHE_EXPIRY = 10 * 60 * 1000 // 10分钟缓存过期时间

/**
 * 生成计算缓存键
 * @param {object} options 计算选项
 * @returns {string} 缓存键
 */
function generateCalculationCacheKey(options = {}) {
  const {
    desktopWidth = 0,
    browserWidth = 0,
    isMobile = false,
    gapRatio = 0.5
  } = options

  return `calc_${desktopWidth}_${browserWidth}_${isMobile}_${gapRatio}`
}

/**
 * 获取缓存的计算结果
 * @param {string} cacheKey 缓存键
 * @returns {object|null} 缓存的计算结果或null
 */
function getCachedCalculation(cacheKey) {
  const cached = CALCULATION_CACHE.get(cacheKey)
  if (!cached) return null

  const now = Date.now()
  if (cached.expiry < now) {
    CALCULATION_CACHE.delete(cacheKey)
    return null
  }

  return cached.result
}

/**
 * 设置计算结果缓存
 * @param {string} cacheKey 缓存键
 * @param {object} result 计算结果
 */
function setCachedCalculation(cacheKey, result) {
  // 如果缓存已满，删除最旧的条目
  if (CALCULATION_CACHE.size >= CALCULATION_CACHE_MAX_SIZE) {
    const firstKey = CALCULATION_CACHE.keys().next().value
    CALCULATION_CACHE.delete(firstKey)
  }

  CALCULATION_CACHE.set(cacheKey, {
    result: { ...result }, // 深拷贝结果
    expiry: Date.now() + CALCULATION_CACHE_EXPIRY,
    timestamp: Date.now()
  })
}

/**
 * 清除过期的缓存条目
 */
function cleanupExpiredCache() {
  const now = Date.now()
  for (const [key, value] of CALCULATION_CACHE.entries()) {
    if (value.expiry < now) {
      CALCULATION_CACHE.delete(key)
    }
  }
}

/**
 * 从接口获取网格布局配置
 * @returns {Promise<object|null>} 返回接口配置或null
 */
async function fetchGridConfig() {
  // 如果正在加载，等待加载完成
  if (API_CONFIG_LOADING) {
    while (API_CONFIG_LOADING) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    return API_CONFIG_CACHE
  }

  // 如果已有缓存，直接返回
  if (API_CONFIG_CACHE) {
    return API_CONFIG_CACHE
  }

  try {
    API_CONFIG_LOADING = true


    // 调用接口获取配置
    const response = await getGridLayoutConfig()

    if (response && response.status === 200 && response.data) {
      const apiConfig = response.data

      // 检查是否有有效的配置数据
      const hasValidData = Object.keys(apiConfig).some(key =>
        Array.isArray(apiConfig[key]) && apiConfig[key].length > 0
      )

      if (hasValidData) {
        API_CONFIG_CACHE = apiConfig

        return apiConfig
      } else {

        return null
      }
    } else {

      return null
    }
  } catch (error) {

    return null
  } finally {
    API_CONFIG_LOADING = false
  }
}

/**
 * 合并接口配置和默认配置
 * @param {object|null} apiConfig 接口配置
 * @returns {object} 合并后的配置
 */
function mergeConfigs(apiConfig) {
  if (!apiConfig) {

    return DEFAULT_CONFIG_MODULES
  }



  // 深拷贝默认配置
  const mergedConfig = JSON.parse(JSON.stringify(DEFAULT_CONFIG_MODULES))

  // 处理新的API数据格式：按分辨率分组的配置
  let hasApiRules = false

  // 遍历API返回的分辨率配置
  Object.keys(apiConfig).forEach(resolutionKey => {
    const apiRules = apiConfig[resolutionKey]

    // 检查是否有有效的规则数据
    if (Array.isArray(apiRules) && apiRules.length > 0) {
      // 直接使用API返回的键名，不需要映射
      if (mergedConfig.LAYOUT_RULES[resolutionKey]) {


        // 转换API数据格式为内部格式
        const convertedRules = apiRules
          .filter(rule => rule.status === 1) // 只使用状态为1的规则
          .map(rule => ({
            minRatio: parseFloat(rule.minration),
            columns: parseInt(rule.columns),
            iconSize: parseInt(rule.iconsize),
            fontSize: parseInt(rule.fontsize),
            lineHeight: parseFloat(rule.lineheight),
            // 新增时间和搜索栏样式字段
            searchBot: rule.searchbot ? parseInt(rule.searchbot) : undefined,
            searchTop: rule.searchtop ? parseInt(rule.searchtop) : undefined,
            timeBot: rule.timebot ? parseInt(rule.timebot) : undefined,
            timeTop: rule.timetop ? parseInt(rule.timetop) : undefined,
            timeFontSize: rule.timefontsize ? parseInt(rule.timefontsize) : undefined,
            timeFontWeight: rule.timefontweight ? parseInt(rule.timefontweight) : undefined
          }))
          .sort((a, b) => b.minRatio - a.minRatio) // 按minRatio降序排列

        if (convertedRules.length > 0) {
          // 完全替换默认配置，不保留任何默认规则
          mergedConfig.LAYOUT_RULES[resolutionKey] = convertedRules
          hasApiRules = true

        }
      } else {

      }
    }
  })

  if (hasApiRules) {

  } else {

  }


  return mergedConfig
}

// 默认模块化配置 - 基础设置
const DEFAULT_CONFIG_MODULES = {
  // 基础参数配置
  BASIC: {
    // 图标间隙比例 R = 图标间隙X2 / 图标宽度X1
    DEFAULT_GAP_RATIO: 0.5,
    // 最小列数
    MIN_COLUMNS: 3,
    // 最大列数
    MAX_COLUMNS: 20,
    // 最小图标尺寸
    MIN_ICON_SIZE: 20,
    // 最大图标尺寸
    MAX_ICON_SIZE: 120
  },

  // 分辨率断点配置
  BREAKPOINTS: {
    UHD_4K: { minWidth: 3840, name: '4K+' },
    QHD_3K: { minWidth: 2880, name: '3K' },
    QHD_2K: { minWidth: 2560, name: '2K' },
    FHD: { minWidth: 1920, name: 'FHD' },
    HD_PLUS: { minWidth: 1440, name: 'HD+' },
    HD: { minWidth: 1280, name: 'HD' },
    XGA: { minWidth: 1024, name: 'XGA' },
    SVGA: { minWidth: 800, name: 'SVGA' }
  },

  // 布局规则配置（包含文字大小）
  LAYOUT_RULES: {
    UHD_4K: [
      { minRatio: 0.75, columns: 16, iconSize: 108, fontSize: 16, lineHeight: 1.2 },
      { minRatio: 0.50, columns: 16, iconSize: 72, fontSize: 14, lineHeight: 1.2 },
      { minRatio: 0.40, columns: 10, iconSize: 90, fontSize: 15, lineHeight: 1.2 },
      { minRatio: 0.30, columns: 10, iconSize: 72, fontSize: 14, lineHeight: 1.2 },
      { minRatio: 0.00, columns: 6, iconSize: 96, fontSize: 15, lineHeight: 1.2 }
    ],
    QHD_3K: [
      { minRatio: 0.72, columns: 16, iconSize: 80, fontSize: 14, lineHeight: 1.2 },
      { minRatio: 0.50, columns: 10, iconSize: 80, fontSize: 14, lineHeight: 1.2 },
      { minRatio: 0.40, columns: 10, iconSize: 72, fontSize: 13, lineHeight: 1.2 },
      { minRatio: 0.35, columns: 10, iconSize: 64, fontSize: 12, lineHeight: 1.2 },
      { minRatio: 0.00, columns: 6, iconSize: 80, fontSize: 14, lineHeight: 1.2 }
    ],
    QHD_2K: [
      { minRatio: 0.72, columns: 16, iconSize: 72, fontSize: 13, lineHeight: 1.2 },
      { minRatio: 0.50, columns: 10, iconSize: 80, fontSize: 14, lineHeight: 1.2 },
      { minRatio: 0.40, columns: 10, iconSize: 64, fontSize: 12, lineHeight: 1.2 },
      { minRatio: 0.30, columns: 6, iconSize: 80, fontSize: 14, lineHeight: 1.2 },
      { minRatio: 0.00, columns: 6, iconSize: 80, fontSize: 14, lineHeight: 1.2 }
    ],
    FHD: [
      { minRatio: 0.80, columns: 16, iconSize: 60, fontSize: 12, lineHeight: 1.2 },
      { minRatio: 0.50, columns: 10, iconSize: 60, fontSize: 12, lineHeight: 1.2 },
      { minRatio: 0.40, columns: 10, iconSize: 48, fontSize: 11, lineHeight: 1.2 },
      { minRatio: 0.30, columns: 6, iconSize: 60, fontSize: 12, lineHeight: 1.2 },
      { minRatio: 0.00, columns: 6, iconSize: 48, fontSize: 11, lineHeight: 1.2 }
    ],
    HD_PLUS: [
      { minRatio: 0.80, columns: 16, iconSize: 46, fontSize: 11, lineHeight: 1.2 },
      { minRatio: 0.50, columns: 10, iconSize: 44, fontSize: 11, lineHeight: 1.2 },
      { minRatio: 0.40, columns: 6, iconSize: 50, fontSize: 11, lineHeight: 1.2 },
      { minRatio: 0.30, columns: 6, iconSize: 46, fontSize: 10, lineHeight: 1.2 },
      { minRatio: 0.00, columns: 6, iconSize: 40, fontSize: 10, lineHeight: 1.2 }
    ],
    HD: [
      { minRatio: 0.85, columns: 16, iconSize: 44, fontSize: 10, lineHeight: 1.2 },
      { minRatio: 0.50, columns: 10, iconSize: 40, fontSize: 10, lineHeight: 1.2 },
      { minRatio: 0.40, columns: 6, iconSize: 48, fontSize: 11, lineHeight: 1.2 },
      { minRatio: 0.30, columns: 6, iconSize: 40, fontSize: 10, lineHeight: 1.2 },
      { minRatio: 0.00, columns: 6, iconSize: 36, fontSize: 9, lineHeight: 1.2 }
    ],
    XGA: [
      { minRatio: 0.90, columns: 16, iconSize: 36, fontSize: 9, lineHeight: 1.2 },
      { minRatio: 0.50, columns: 10, iconSize: 32, fontSize: 9, lineHeight: 1.2 },
      { minRatio: 0.40, columns: 6, iconSize: 36, fontSize: 9, lineHeight: 1.2 },
      { minRatio: 0.30, columns: 6, iconSize: 32, fontSize: 8, lineHeight: 1.2 },
      { minRatio: 0.00, columns: 6, iconSize: 28, fontSize: 8, lineHeight: 1.2 }
    ],
    SVGA: [
      { minRatio: 0.90, columns: 16, iconSize: 30, fontSize: 8, lineHeight: 1.2 },
      { minRatio: 0.50, columns: 10, iconSize: 24, fontSize: 8, lineHeight: 1.2 },
      { minRatio: 0.40, columns: 6, iconSize: 32, fontSize: 8, lineHeight: 1.2 },
      { minRatio: 0.30, columns: 6, iconSize: 24, fontSize: 7, lineHeight: 1.2 },
      { minRatio: 0.00, columns: 6, iconSize: 22, fontSize: 7, lineHeight: 1.2 }
    ]
  },

  // 文字样式配置
  TYPOGRAPHY: {
    // 基础文字设置
    BASE_FONT_FAMILY: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    BASE_FONT_WEIGHT: '400',
    BASE_TEXT_ALIGN: 'center',
    BASE_COLOR: '#333333',

    // 文字大小映射规则（基于图标尺寸）
    FONT_SIZE_MAPPING: {
      // 图标尺寸范围 -> 文字大小
      ranges: [
        { minIconSize: 100, fontSize: 16, lineHeight: 1.2, description: '超大图标' },
        { minIconSize: 80, fontSize: 14, lineHeight: 1.2, description: '大图标' },
        { minIconSize: 60, fontSize: 12, lineHeight: 1.2, description: '中等图标' },
        { minIconSize: 45, fontSize: 11, lineHeight: 1.2, description: '小图标' },
        { minIconSize: 35, fontSize: 10, lineHeight: 1.2, description: '很小图标' },
        { minIconSize: 25, fontSize: 9, lineHeight: 1.2, description: '极小图标' },
        { minIconSize: 0, fontSize: 8, lineHeight: 1.2, description: '最小图标' }
      ]
    },

    // 特殊场景文字调整
    SPECIAL_CASES: {
      // 手机模式文字设置
      MOBILE: {
        fontSize: 12,
        lineHeight: 1.3,
        fontWeight: '500',
        maxLines: 2
      },

      // 高DPI屏幕调整
      HIGH_DPI: {
        fontSizeMultiplier: 1.1,
        fontWeight: '500'
      }
    }
  },

  // 预设模式配置
  PRESETS: {
    DEFAULT: { gapRatio: 0.5, name: '默认模式' },
    COMPACT: { gapRatio: 0.3, columnMultiplier: 1.2, iconSizeMultiplier: 0.8, fontSizeMultiplier: 0.9, name: '紧凑模式' },
    SPACIOUS: { gapRatio: 0.7, columnMultiplier: 0.8, iconSizeMultiplier: 1.2, fontSizeMultiplier: 1.1, name: '宽松模式' },
    MOBILE: { gapRatio: 0.6, maxColumns: 8, minIconSize: 48, fontSizeMultiplier: 1.0, name: '移动优化' }
  }
}

// 构建默认配置
function buildDefaultConfig() {
  return {
    gapRatio: DEFAULT_CONFIG_MODULES.BASIC.DEFAULT_GAP_RATIO,
    resolutionConfigs: Object.keys(DEFAULT_CONFIG_MODULES.BREAKPOINTS).map(key => ({
      ...DEFAULT_CONFIG_MODULES.BREAKPOINTS[key],
      rules: DEFAULT_CONFIG_MODULES.LAYOUT_RULES[key]
    }))
  }
}

// 构建配置（支持接口配置）
async function buildConfig() {
  try {
    // 尝试从接口获取配置
    const apiConfig = await fetchGridConfig()

    // 合并配置
    const mergedModules = mergeConfigs(apiConfig)

    return {
      gapRatio: mergedModules.BASIC.DEFAULT_GAP_RATIO,
      resolutionConfigs: Object.keys(mergedModules.BREAKPOINTS).map(key => ({
        ...mergedModules.BREAKPOINTS[key],
        rules: mergedModules.LAYOUT_RULES[key]
      }))
    }
  } catch (error) {
    console.warn('⚠️ 构建配置失败，使用默认配置:', error.message)
    return buildDefaultConfig()
  }
}

/**
 * 网格布局计算器类
 */
class GridLayoutCalculator {
  constructor(config = {}) {
    this.config = { ...buildDefaultConfig(), ...config }
    this.configLoaded = false
  }

  /**
   * 异步初始化配置
   * @returns {Promise<void>}
   */
  async initConfig() {
    if (this.configLoaded) {

      return
    }

    try {

      const builtConfig = await buildConfig()
      // 修复：应该让 builtConfig 覆盖默认配置，而不是相反
      this.config = { ...this.config, ...builtConfig }
      this.configLoaded = true

    } catch (error) {
      console.warn('⚠️ 配置初始化失败，使用默认配置:', error.message)
      this.config = buildDefaultConfig()
      this.configLoaded = true
    }
  }

  /**
   * 强制重新加载配置（用于调试）
   * @returns {Promise<void>}
   */
  async forceReloadConfig() {

    // 清除API配置缓存
    API_CONFIG_CACHE = null
    this.configLoaded = false
    await this.initConfig()
  }

  /**
   * 获取桌面显示器真实物理宽度 W1
   * @returns {number} 桌面显示器真实物理宽度
   */
  getDesktopWidth() {
    // 获取设备像素比
    const devicePixelRatio = window.devicePixelRatio || 1

    // 计算真实的物理分辨率
    if (window.screen && window.screen.width) {
      // 使用 screen.width * devicePixelRatio 获取真实物理像素宽度
      const physicalWidth = Math.round(window.screen.width * devicePixelRatio)

      return physicalWidth
    }

    // 备用方案：使用可用屏幕宽度计算物理像素
    const fallbackWidth = Math.round((window.screen?.availWidth || window.innerWidth) * devicePixelRatio)

    return fallbackWidth
  }

  /**
   * 获取浏览器窗口真实物理宽度 W2
   * @returns {number} 浏览器窗口真实物理宽度
   */
  getBrowserWidth() {
    // 获取设备像素比
    const devicePixelRatio = window.devicePixelRatio || 1

    // 计算浏览器窗口的真实物理像素宽度
    const physicalWidth = Math.round(window.innerWidth * devicePixelRatio)

    return physicalWidth
  }

  /**
   * 获取设备像素比和分辨率信息
   * @returns {object} 设备信息
   */
  getDeviceInfo() {
    const devicePixelRatio = window.devicePixelRatio || 1
    const logicalScreenWidth = window.screen?.width || 0
    const logicalScreenHeight = window.screen?.height || 0
    const logicalWindowWidth = window.innerWidth
    const logicalWindowHeight = window.innerHeight

    return {
      devicePixelRatio,
      logical: {
        screenWidth: logicalScreenWidth,
        screenHeight: logicalScreenHeight,
        windowWidth: logicalWindowWidth,
        windowHeight: logicalWindowHeight
      },
      physical: {
        screenWidth: Math.round(logicalScreenWidth * devicePixelRatio),
        screenHeight: Math.round(logicalScreenHeight * devicePixelRatio),
        windowWidth: Math.round(logicalWindowWidth * devicePixelRatio),
        windowHeight: Math.round(logicalWindowHeight * devicePixelRatio)
      }
    }
  }

  /**
   * 计算窗口占比 P = W2/W1
   * @param {number} desktopWidth 桌面宽度 W1
   * @param {number} browserWidth 浏览器宽度 W2
   * @returns {number} 窗口占比
   */
  calculateWindowRatio(desktopWidth, browserWidth) {
    return browserWidth / desktopWidth
  }

  /**
   * 根据桌面宽度找到对应的配置
   * @param {number} desktopWidth 桌面宽度
   * @returns {object|null} 匹配的配置
   */
  findResolutionConfig(desktopWidth) {
    return this.config.resolutionConfigs.find(config => 
      desktopWidth >= config.minWidth
    ) || this.config.resolutionConfigs[this.config.resolutionConfigs.length - 1]
  }

  /**
   * 根据窗口占比找到对应的规则
   * @param {object} resolutionConfig 分辨率配置
   * @param {number} windowRatio 窗口占比
   * @returns {object} 匹配的规则
   */
  findRule(resolutionConfig, windowRatio) {
    return resolutionConfig.rules.find(rule => 
      windowRatio >= rule.minRatio
    ) || resolutionConfig.rules[resolutionConfig.rules.length - 1]
  }

  /**
   * 计算网格区域宽度 W3 = X1 * (N + (N-1) * R)
   * @param {number} iconSize 图标尺寸 X1
   * @param {number} columns 列数 N
   * @param {number} gapRatio 间隙比例 R
   * @returns {number} 网格区域宽度
   */
  calculateGridWidth(iconSize, columns, gapRatio) {
    const gap = iconSize * gapRatio
    return iconSize * columns + gap * (columns - 1)
  }

  /**
   * 计算网格区占比 Q = W3/W2
   * @param {number} gridWidth 网格区域宽度 W3
   * @param {number} browserWidth 浏览器宽度 W2
   * @returns {number} 网格区占比
   */
  calculateGridRatio(gridWidth, browserWidth) {
    return gridWidth / browserWidth
  }

  /**
   * 计算时间组件样式
   * @param {object} rule 当前匹配的规则
   * @param {object} options 选项
   * @returns {object} 时间组件样式配置
   */
  calculateTimeStyle(rule, options = {}) {
    const { isMobile = false } = options

    // 手机模式使用默认样式
    if (isMobile) {
      return {
        marginTop: '20px',
        marginBottom: '15px',
        fontSize: '2.8rem',
        fontWeight: 700
      }
    }

    // 使用API配置或默认值
    const marginTop = rule.timeTop ? `${rule.timeTop}px` : '20px'
    const marginBottom = rule.timeBot ? `${rule.timeBot}px` : '15px'
    const fontSize = rule.timeFontSize ? `${rule.timeFontSize}px` : '2.8rem'
    const fontWeight = rule.timeFontWeight || 700

    return {
      marginTop,
      marginBottom,
      fontSize,
      fontWeight
    }
  }

  /**
   * 计算搜索栏样式
   * @param {object} rule 当前匹配的规则
   * @param {object} options 选项
   * @returns {object} 搜索栏样式配置
   */
  calculateSearchStyle(rule, options = {}) {
    const { isMobile = false } = options

    // 手机模式使用默认样式
    if (isMobile) {
      return {
        marginTop: '15px',
        marginBottom: '20px'
      }
    }

    // 使用API配置或默认值
    const marginTop = rule.searchTop ? `${rule.searchTop}px` : '15px'
    const marginBottom = rule.searchBot ? `${rule.searchBot}px` : '20px'

    return {
      marginTop,
      marginBottom
    }
  }

  /**
   * 根据图标尺寸计算最佳文字大小
   * @param {number} iconSize 图标尺寸
   * @param {object} options 选项
   * @returns {object} 文字样式配置
   */
  calculateFontStyle(iconSize, options = {}) {
    const { isMobile = false, dpr = window.devicePixelRatio || 1, rule = null } = options

    // 手机模式特殊处理
    if (isMobile) {
      const mobileConfig = DEFAULT_CONFIG_MODULES.TYPOGRAPHY.SPECIAL_CASES.MOBILE
      return {
        fontSize: mobileConfig.fontSize,
        lineHeight: mobileConfig.lineHeight,
        fontWeight: mobileConfig.fontWeight,
        maxLines: mobileConfig.maxLines,
        fontFamily: DEFAULT_CONFIG_MODULES.TYPOGRAPHY.BASE_FONT_FAMILY,
        textAlign: DEFAULT_CONFIG_MODULES.TYPOGRAPHY.BASE_TEXT_ALIGN,
        color: DEFAULT_CONFIG_MODULES.TYPOGRAPHY.BASE_COLOR
      }
    }

    // 优先使用接口配置中的文字设置
    let fontSize, lineHeight, description

    if (rule && rule.fontSize && rule.lineHeight) {
      // 使用接口配置的文字设置
      fontSize = rule.fontSize
      lineHeight = rule.lineHeight
      description = '接口配置'

    } else {
      // 根据图标尺寸查找对应的文字大小（默认配置）
      const ranges = DEFAULT_CONFIG_MODULES.TYPOGRAPHY.FONT_SIZE_MAPPING.ranges
      const matchedRange = ranges.find(range => iconSize >= range.minIconSize) || ranges[ranges.length - 1]
      fontSize = matchedRange.fontSize
      lineHeight = matchedRange.lineHeight
      description = matchedRange.description

    }

    // 高DPI屏幕调整
    if (dpr >= 1.5) {
      const highDpiConfig = DEFAULT_CONFIG_MODULES.TYPOGRAPHY.SPECIAL_CASES.HIGH_DPI
      fontSize = Math.round(fontSize * highDpiConfig.fontSizeMultiplier)
    }

    return {
      fontSize,
      lineHeight,
      fontWeight: DEFAULT_CONFIG_MODULES.TYPOGRAPHY.BASE_FONT_WEIGHT,
      fontFamily: DEFAULT_CONFIG_MODULES.TYPOGRAPHY.BASE_FONT_FAMILY,
      textAlign: DEFAULT_CONFIG_MODULES.TYPOGRAPHY.BASE_TEXT_ALIGN,
      color: DEFAULT_CONFIG_MODULES.TYPOGRAPHY.BASE_COLOR,
      description: description
    }
  }

  /**
   * 主计算函数：根据当前环境计算最佳网格布局参数（支持缓存）
   * @param {object} options 可选参数
   * @returns {Promise<object>} 计算结果
   */
  async calculate(options = {}) {
    // 确保配置已初始化
    await this.initConfig()

    // 获取基础参数（使用物理像素）
    const W1 = options.desktopWidth || this.getDesktopWidth()
    const W2 = options.browserWidth || this.getBrowserWidth()
    const R = options.gapRatio || this.config.gapRatio

    // 检查是否为手机模式（使用逻辑像素判断，因为手机模式主要看屏幕尺寸而非物理像素）
    const logicalWindowWidth = window.innerWidth
    const isMobile = options.isMobile !== undefined ? options.isMobile : (logicalWindowWidth <= 480)

    // 生成缓存键
    const cacheKey = generateCalculationCacheKey({
      desktopWidth: W1,
      browserWidth: W2,
      isMobile,
      gapRatio: R
    })

    // 尝试从缓存获取结果
    const cachedResult = getCachedCalculation(cacheKey)
    if (cachedResult) {
      console.log('使用缓存的网格计算结果:', cacheKey)
      return cachedResult
    }

    // 缓存未命中，进行实际计算
    let result

    // 手机模式特殊处理
    if (isMobile) {
      result = this.calculateMobileLayout(W1, W2, R, options)
    } else {
      // 获取设备信息
      const deviceInfo = this.getDeviceInfo()
      result = await this.calculateDesktopLayout(W1, W2, R, deviceInfo, options)
    }

    // 将结果存入缓存
    setCachedCalculation(cacheKey, result)

    // 定期清理过期缓存
    if (Math.random() < 0.1) { // 10%的概率触发清理
      cleanupExpiredCache()
    }

    console.log('完成网格计算并缓存:', cacheKey)
    return result
  }

  /**
   * 桌面模式布局计算（从原calculate函数中提取）
   * @param {number} W1 桌面宽度
   * @param {number} W2 浏览器宽度
   * @param {number} R 间隙比例
   * @param {object} deviceInfo 设备信息
   * @param {object} options 选项
   * @returns {object} 计算结果
   */
  async calculateDesktopLayout(W1, W2, R, deviceInfo, options = {}) {

    // 计算窗口占比
    const P = this.calculateWindowRatio(W1, W2)

    // 找到对应的分辨率配置
    const resolutionConfig = this.findResolutionConfig(W1)

    // 根据窗口占比找到对应的规则
    const rule = this.findRule(resolutionConfig, P)

    // 提取参数，直接使用配置中的图标尺寸，不进行任何缩放
    const N = rule.columns  // 图标数量（列数）
    const rawIconSize = rule.iconSize  // 配置中的原始图标尺寸
    const X1 = rawIconSize  // 直接使用配置中的图标尺寸，不进行DPR缩放



    // 计算网格区域宽度和占比
    const W3 = this.calculateGridWidth(X1, N, R)
    const Q = this.calculateGridRatio(W3, W2)

    // 计算实际间隙尺寸
    const X2 = X1 * R  // 间隙尺寸

    // 计算文字样式 - 优先使用接口配置中的文字设置
    const fontStyle = this.calculateFontStyle(X1, {
      isMobile: false,
      dpr: window.devicePixelRatio || 1,
      rule: rule  // 传入当前规则，以便使用接口配置的文字设置
    })

    // 计算时间组件样式
    const timeStyle = this.calculateTimeStyle(rule, { isMobile: false })

    // 计算搜索栏样式
    const searchStyle = this.calculateSearchStyle(rule, { isMobile: false })

    return {
      // 输入参数
      W1,  // 桌面显示器物理宽度
      W2,  // 浏览器窗口物理宽度
      R,   // 图标间隙比例

      // 计算结果
      P,   // 窗口占比
      N,   // 图标数量（列数）
      Q,   // 网格区占比
      W3,  // 网格区域宽度
      X1,  // 图标尺寸（直接使用配置值）
      X2,  // 间隙尺寸
      rawIconSize,  // 原始图标尺寸（与X1相同）
      scaleRatio: 1,  // 不进行缩放，固定为1

      // 文字样式
      fontSize: fontStyle.fontSize,
      lineHeight: fontStyle.lineHeight,
      fontWeight: fontStyle.fontWeight,
      fontFamily: fontStyle.fontFamily,
      textAlign: fontStyle.textAlign,
      textColor: fontStyle.color,

      // 设备信息
      deviceInfo,

      // 额外信息
      resolutionName: resolutionConfig.name,
      matchedRule: rule,
      isMobile: false,
      fontStyleDescription: fontStyle.description,

      // CSS 样式对象
      gridStyle: {
        'grid-template-columns': `repeat(${N}, ${X1}px)`,
        'grid-auto-rows': `${X1}px`,
        'gap': `${X2}px`,
        'max-width': `${W3}px`,
        'margin': '0 auto'
      },

      // 文字CSS样式对象
      textStyle: {
        'font-size': `${fontStyle.fontSize}px`,
        'line-height': fontStyle.lineHeight,
        'font-weight': fontStyle.fontWeight,
        'font-family': fontStyle.fontFamily,
        'text-align': fontStyle.textAlign,
        'color': fontStyle.color
      },

      // 时间组件样式对象
      timeStyle: {
        'margin-top': timeStyle.marginTop,
        'margin-bottom': timeStyle.marginBottom,
        'font-size': timeStyle.fontSize,
        'font-weight': timeStyle.fontWeight
      },

      // 搜索栏样式对象
      searchStyle: {
        'margin-top': searchStyle.marginTop,
        'margin-bottom': searchStyle.marginBottom
      }
    }
  }

  /**
   * 手机模式布局计算
   * @param {number} W1 桌面宽度
   * @param {number} W2 浏览器宽度
   * @param {number} R 间隙比例
   * @param {object} options 选项
   * @returns {object} 计算结果
   */
  calculateMobileLayout(W1, W2, R, options = {}) {
    // 手机模式固定显示6列
    const N = 6

    // 获取设备信息
    const deviceInfo = this.getDeviceInfo()

    // 手机模式使用逻辑像素进行布局计算，确保UI元素大小合适
    const logicalWindowWidth = window.innerWidth
    const usableWidth = logicalWindowWidth - 20  // 预留10px左右边距



    // 计算图标尺寸: (可用宽度 - (列数-1)*间隙) / 列数
    // 其中间隙 = 图标尺寸 * R，所以：
    // 图标尺寸 = (可用宽度) / (列数 + (列数-1)*R)
    const X1 = Math.floor(usableWidth / (N + (N - 1) * R))
    const X2 = X1 * R  // 间隙尺寸

    // 确保图标尺寸在合理范围内，并根据DPR进行缩放
    const dprScaledX1 = Math.round(X1 / deviceInfo.devicePixelRatio)
    const finalX1 = Math.max(40, Math.min(80, dprScaledX1))  // 图标尺寸限制在40-80px之间
    const finalX2 = finalX1 * R



    // 计算网格区域宽度和占比（手机模式使用逻辑像素）
    const W3 = this.calculateGridWidth(finalX1, N, R)
    const Q = this.calculateGridRatio(W3, logicalWindowWidth)  // 使用逻辑窗口宽度
    const P = this.calculateWindowRatio(W1, W2)  // 窗口占比仍使用物理像素比较

    // 计算手机模式文字样式
    const fontStyle = this.calculateFontStyle(finalX1, {
      isMobile: true,
      rule: null,  // 手机模式使用默认文字配置
      dpr: window.devicePixelRatio || 1
    })

    // 计算手机模式时间组件样式
    const timeStyle = this.calculateTimeStyle({}, { isMobile: true })

    // 计算手机模式搜索栏样式
    const searchStyle = this.calculateSearchStyle({}, { isMobile: true })

    return {
      // 输入参数
      W1,  // 桌面显示器宽度
      W2,  // 浏览器窗口宽度
      R,   // 图标间隙比例

      // 计算结果
      P,   // 窗口占比
      N,   // 图标数量（列数）
      Q,   // 网格区占比
      W3,  // 网格区域宽度
      X1: finalX1,  // 图标尺寸（已根据DPR缩放）
      X2: finalX2,  // 间隙尺寸
      rawIconSize: X1,  // 原始计算的图标尺寸（手机模式）
      scaleRatio: X1 ? (X1 / finalX1) : 1,  // 缩放倍数

      // 文字样式
      fontSize: fontStyle.fontSize,
      lineHeight: fontStyle.lineHeight,
      fontWeight: fontStyle.fontWeight,
      fontFamily: fontStyle.fontFamily,
      textAlign: fontStyle.textAlign,
      textColor: fontStyle.color,
      maxLines: fontStyle.maxLines,

      // 设备信息
      deviceInfo,

      // 额外信息
      resolutionName: 'Mobile',
      matchedRule: { minRatio: 0, columns: N, iconSize: finalX1 },
      isMobile: true,
      fontStyleDescription: '手机模式',

      // CSS 样式对象
      gridStyle: {
        'grid-template-columns': `repeat(${N}, ${finalX1}px)`,
        'grid-auto-rows': `${finalX1}px`,
        'gap': `${finalX2}px`,
        'max-width': `${W3}px`,
        'margin': '0 auto'
      },

      // 文字CSS样式对象
      textStyle: {
        'font-size': `${fontStyle.fontSize}px`,
        'line-height': fontStyle.lineHeight,
        'font-weight': fontStyle.fontWeight,
        'font-family': fontStyle.fontFamily,
        'text-align': fontStyle.textAlign,
        'color': fontStyle.color,
        'max-lines': fontStyle.maxLines || 'unset'
      },

      // 时间组件样式对象（手机模式）
      timeStyle: {
        'margin-top': timeStyle.marginTop,
        'margin-bottom': timeStyle.marginBottom,
        'font-size': timeStyle.fontSize,
        'font-weight': timeStyle.fontWeight
      },

      // 搜索栏样式对象（手机模式）
      searchStyle: {
        'margin-top': searchStyle.marginTop,
        'margin-bottom': searchStyle.marginBottom
      }
    }
  }

  /**
   * 更新配置
   * @param {object} newConfig 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 获取当前配置
   * @returns {object} 当前配置
   */
  getConfig() {
    return { ...this.config }
  }

  /**
   * 应用预设模式
   * @param {string} presetName 预设名称
   */
  applyPreset(presetName) {
    const preset = DEFAULT_CONFIG_MODULES.PRESETS[presetName.toUpperCase()]
    if (!preset) {

      return
    }

    const newConfig = { ...this.config, ...preset }

    // 如果有倍数设置，应用到规则上
    if (preset.columnMultiplier || preset.iconSizeMultiplier) {
      newConfig.resolutionConfigs = this.config.resolutionConfigs.map(config => ({
        ...config,
        rules: config.rules.map(rule => ({
          ...rule,
          columns: preset.columnMultiplier ?
            Math.max(DEFAULT_CONFIG_MODULES.BASIC.MIN_COLUMNS,
              Math.min(DEFAULT_CONFIG_MODULES.BASIC.MAX_COLUMNS,
                Math.round(rule.columns * preset.columnMultiplier))) : rule.columns,
          iconSize: preset.iconSizeMultiplier ?
            Math.max(preset.minIconSize || DEFAULT_CONFIG_MODULES.BASIC.MIN_ICON_SIZE,
              Math.min(DEFAULT_CONFIG_MODULES.BASIC.MAX_ICON_SIZE,
                Math.round(rule.iconSize * preset.iconSizeMultiplier))) : rule.iconSize
        }))
      }))
    }

    this.config = newConfig
  }

  /**
   * 获取可用的预设模式
   * @returns {array} 预设模式列表
   */
  getAvailablePresets() {
    return Object.keys(DEFAULT_CONFIG_MODULES.PRESETS).map(key => ({
      key: key.toLowerCase(),
      name: DEFAULT_CONFIG_MODULES.PRESETS[key].name,
      config: DEFAULT_CONFIG_MODULES.PRESETS[key]
    }))
  }
}

// 创建默认实例
const defaultCalculator = new GridLayoutCalculator()

// 便捷工具函数
const GridUtils = {
  /**
   * 快速计算当前环境的网格参数
   * @param {object} options 可选参数
   * @returns {Promise<object>} 计算结果
   */
  async quickCalculate(options = {}) {
    return await defaultCalculator.calculate(options)
  },

  /**
   * 获取适用于Vue的响应式网格样式
   * @param {object} options 可选参数
   * @returns {Promise<object>} Vue样式对象
   */
  async getVueGridStyle(options = {}) {
    const result = await defaultCalculator.calculate(options)
    return {
      ...result.gridStyle,
      'grid-auto-flow': 'dense',
      'padding-bottom': options.paddingBottom || '20px',
      'margin': '0 auto',
      'overflow-x': 'hidden'
    }
  },

  /**
   * 检测是否为手机模式（使用逻辑像素）
   * @param {number} width 窗口宽度，默认使用当前窗口逻辑宽度
   * @returns {boolean} 是否为手机模式
   */
  isMobileMode(width = window.innerWidth) {
    // 手机模式检测使用逻辑像素，因为主要看屏幕尺寸而非物理像素密度
    const isMobile = width <= 480
    return isMobile
  },

  /**
   * 应用文字样式到DOM元素
   * @param {string} selector CSS选择器
   * @param {object} textStyle 文字样式对象
   */
  applyTextStyle(selector, textStyle) {
    const elements = document.querySelectorAll(selector)
    elements.forEach(element => {
      Object.keys(textStyle).forEach(property => {
        if (textStyle[property] !== undefined) {
          element.style[property] = textStyle[property]
        }
      })
    })
  },

  /**
   * 获取当前环境的文字样式
   * @param {object} options 可选参数
   * @returns {Promise<object>} 文字样式对象
   */
  async getCurrentTextStyle(options = {}) {
    const result = await defaultCalculator.calculate(options)
    return result.textStyle
  },

  /**
   * 获取当前环境的时间组件样式
   * @param {object} options 可选参数
   * @returns {Promise<object>} 时间组件样式对象
   */
  async getTimeStyle(options = {}) {
    const result = await defaultCalculator.calculate(options)
    return result.timeStyle
  },

  /**
   * 获取当前环境的搜索栏样式
   * @param {object} options 可选参数
   * @returns {Promise<object>} 搜索栏样式对象
   */
  async getSearchStyle(options = {}) {
    const result = await defaultCalculator.calculate(options)
    return result.searchStyle
  },

  /**
   * 自动应用文字样式到应用名称
   * @param {object} options 可选参数
   */
  async autoApplyTextStyle(options = {}) {
    const textStyle = await GridUtils.getCurrentTextStyle(options)

    // 应用到应用名称
    GridUtils.applyTextStyle('.app-name', textStyle)

    // 应用到其他文字元素（可根据需要扩展）
    GridUtils.applyTextStyle('.app-title', textStyle)
    GridUtils.applyTextStyle('.grid-text', textStyle)

    return textStyle
  },

  /**
   * 创建自定义配置的计算器
   * @param {string|object} preset 预设名称或自定义配置
   * @returns {GridLayoutCalculator} 计算器实例
   */
  createCalculator(preset) {
    const calculator = new GridLayoutCalculator()

    if (typeof preset === 'string') {
      calculator.applyPreset(preset)
    } else if (typeof preset === 'object') {
      calculator.updateConfig(preset)
    }

    return calculator
  },

  /**
   * 监听窗口变化并自动更新网格
   * @param {function} callback 更新回调函数
   * @param {number} debounceMs 防抖延迟
   * @param {boolean} autoApplyTextStyle 是否自动应用文字样式
   * @returns {function} 清理函数
   */
  watchResize(callback, debounceMs = 100, autoApplyTextStyle = true) {
    let timer = null

    const handleResize = () => {
      clearTimeout(timer)
      timer = setTimeout(async () => {
        // 检测当前是否为手机模式
        const isMobile = GridUtils.isMobileMode()
        const result = await defaultCalculator.calculate({ isMobile })

        // 自动应用文字样式
        if (autoApplyTextStyle) {
          await GridUtils.autoApplyTextStyle({ isMobile })
        }

        callback(result)
      }, debounceMs)
    }

    window.addEventListener('resize', handleResize)

    // 立即执行一次
    handleResize()

    // 返回清理函数
    return () => {
      clearTimeout(timer)
      window.removeEventListener('resize', handleResize)
    }
  },

  /**
   * 获取配置模块（用于自定义配置）
   * @returns {object} 配置模块
   */
  getConfigModules() {
    return DEFAULT_CONFIG_MODULES
  },
}

// 强制重新加载配置的调试函数
window.forceReloadGridConfig = async function() {
  if (window.gridCalculator) {
    await window.gridCalculator.forceReloadConfig()
    const result = await window.gridCalculator.calculate()
    return result
  }
}

// 清除API配置缓存的调试函数
window.clearGridConfigCache = function() {
  if (window.gridCalculator) {
    window.gridCalculator.forceReloadConfig()
  }
}

// 导出
export {
  GridLayoutCalculator,
  DEFAULT_CONFIG_MODULES,
  GridUtils,
  defaultCalculator
}
export default defaultCalculator
